{% extends "base.html" %}

{% block title %}{{ report.project_name }} - 研究报告 - 项目研究报告平台{% endblock %}

{% block extra_css %}
<style>
    .report-container {
        background: #fff;
        border-radius: 0.5rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        overflow: hidden;
    }
    
    .report-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        padding: 2rem;
    }
    
    .report-content {
        padding: 2rem;
        line-height: 1.8;
    }
    
    .report-content h1,
    .report-content h2,
    .report-content h3,
    .report-content h4,
    .report-content h5,
    .report-content h6 {
        color: #2c3e50;
        margin-top: 2rem;
        margin-bottom: 1rem;
        font-weight: 600;
    }
    
    .report-content h1 {
        border-bottom: 3px solid #28a745;
        padding-bottom: 0.5rem;
    }
    
    .report-content h2 {
        border-bottom: 2px solid #6c757d;
        padding-bottom: 0.3rem;
    }
    
    .report-content p {
        margin-bottom: 1.2rem;
        text-align: justify;
    }
    
    .report-content blockquote {
        border-left: 4px solid #28a745;
        background-color: #f8f9fa;
        padding: 1rem 1.5rem;
        margin: 1.5rem 0;
        font-style: italic;
    }
    
    .report-content code {
        background-color: #f8f9fa;
        color: #e83e8c;
        padding: 0.2rem 0.4rem;
        border-radius: 0.25rem;
        font-size: 0.875em;
    }
    
    .report-content pre {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 0.375rem;
        padding: 1rem;
        overflow-x: auto;
        margin: 1.5rem 0;
    }
    
    .report-content pre code {
        background: none;
        color: inherit;
        padding: 0;
    }
    
    .report-content table {
        width: 100%;
        margin: 1.5rem 0;
        border-collapse: collapse;
        border: 1px solid #dee2e6;
    }
    
    .report-content table th,
    .report-content table td {
        padding: 0.75rem;
        border: 1px solid #dee2e6;
        text-align: left;
    }
    
    .report-content table th {
        background-color: #f8f9fa;
        font-weight: 600;
        color: #495057;
    }
    
    .report-content table tbody tr:nth-child(even) {
        background-color: #f8f9fa;
    }
    
    .report-content ul,
    .report-content ol {
        margin: 1rem 0;
        padding-left: 2rem;
    }
    
    .report-content li {
        margin-bottom: 0.5rem;
    }
    
    .report-content img {
        max-width: 100%;
        height: auto;
        border-radius: 0.375rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        margin: 1rem 0;
    }
    
    .report-content a {
        color: #28a745;
        text-decoration: none;
        border-bottom: 1px dotted #28a745;
    }
    
    .report-content a:hover {
        color: #1e7e34;
        border-bottom-style: solid;
    }
    
    .back-button {
        margin-bottom: 1rem;
    }
    
    .project-meta {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 0.375rem;
        padding: 1rem;
        margin-top: 1rem;
    }
    
    .project-meta .meta-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
    }
    
    .project-meta .meta-item:last-child {
        margin-bottom: 0;
    }
    
    .project-meta .meta-label {
        font-weight: 500;
        opacity: 0.9;
    }
    
    .project-meta .meta-value {
        font-weight: 600;
    }
    
    .action-buttons {
        margin-top: 1.5rem;
    }
    
    .action-buttons .btn {
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
    }
    
    .table-of-contents {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .table-of-contents h4 {
        margin-top: 0;
        margin-bottom: 1rem;
        color: #495057;
    }
    
    .table-of-contents ul {
        margin: 0;
        padding-left: 1.5rem;
    }
    
    .table-of-contents a {
        color: #495057;
        text-decoration: none;
    }
    
    .table-of-contents a:hover {
        color: #28a745;
    }
    
    @media (max-width: 768px) {
        .report-header {
            padding: 1.5rem 1rem;
        }
        
        .report-content {
            padding: 1.5rem 1rem;
        }
        
        .project-meta .meta-item {
            flex-direction: column;
            gap: 0.25rem;
        }
        
        .action-buttons .btn {
            width: 100%;
            margin-right: 0;
        }
        
        .report-content table {
            font-size: 0.875rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- Back Button -->
        <div class="back-button">
            <a href="{{ url_for('public.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>返回列表
            </a>
        </div>

        <!-- Report Container -->
        <div class="report-container">
            <!-- Header -->
            <div class="report-header">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1 class="h2 mb-0">
                            <i class="fas fa-file-text me-2"></i>{{ report.project_name }}
                        </h1>
                        <p class="mb-0 mt-2 opacity-75">Web3项目深度分析</p>
                    </div>
                    <div class="col-md-4">
                        <div class="project-meta">
                            <div class="meta-item">
                                <span class="meta-label">创建时间:</span>
                                <span class="meta-value">
                                    {{ report.created_at | datetime('%Y-%m-%d') if report.created_at else '未知' }}
                                </span>
                            </div>
                            <div class="meta-item">
                                <span class="meta-label">最后更新:</span>
                                <span class="meta-value">
                                    {{ report.updated_at | datetime('%Y-%m-%d') if report.updated_at else '未知' }}
                                </span>
                            </div>
                        </div>
                        
                        <div class="action-buttons">
                            <a href="{{ url_for('public.view_analysis', report_id=report.id) }}" 
                               class="btn btn-light btn-sm">
                                <i class="fas fa-chart-bar me-1"></i>查看分析
                            </a>
                            {% if report.official_website %}
                            <a href="{{ report.official_website }}" target="_blank" 
                               class="btn btn-light btn-sm">
                                <i class="fas fa-external-link-alt me-1"></i>官方网站
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Report Content -->
            <div class="report-content">
                {% if report_content %}
                    {{ report_content | safe }}
                {% else %}
                    <div class="text-center py-5">
                        <div class="empty-state">
                            <i class="fas fa-exclamation-triangle text-warning"></i>
                            <h3>报告内容不可用</h3>
                            <p class="text-muted">
                                抱歉，该项目的研究报告暂时无法显示。<br>
                                请稍后重试或联系管理员。
                            </p>
                            <a href="{{ url_for('public.view_analysis', report_id=report.id) }}" 
                               class="btn btn-primary">
                                <i class="fas fa-chart-bar me-1"></i>查看分析页面
                            </a>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Additional Information -->
        <!-- {% if report.description %}
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>项目描述
                </h5>
            </div>
            <div class="card-body">
                <p class="mb-0">{{ report.description }}</p>
            </div>
        </div>
        {% endif %} -->

        <!-- Navigation -->
        <div class="d-flex justify-content-between mt-4">
            <a href="{{ url_for('public.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-list me-1"></i>返回列表
            </a>
            <a href="{{ url_for('public.view_analysis', report_id=report.id) }}" class="btn btn-primary">
                <i class="fas fa-chart-bar me-1"></i>查看分析页面
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 生成目录
    generateTableOfContents();
    
    // 平滑滚动
    $('a[href^="#"]').on('click', function(e) {
        e.preventDefault();
        const target = $(this.getAttribute('href'));
        if (target.length) {
            $('html, body').animate({
                scrollTop: target.offset().top - 100
            }, 500);
        }
    });
    
    // 代码块复制功能
    addCopyButtonsToCodeBlocks();
    
    // 图片点击放大
    addImageClickHandler();
});

// 生成目录
function generateTableOfContents() {
    const headings = $('.report-content').find('h1, h2, h3, h4, h5, h6');
    
    if (headings.length > 3) {
        let tocHtml = '<div class="table-of-contents"><h4><i class="fas fa-list me-2"></i>目录</h4><ul>';
        
        headings.each(function(index) {
            const heading = $(this);
            const id = 'heading-' + index;
            heading.attr('id', id);
            
            const level = parseInt(heading.prop('tagName').substring(1));
            const indent = level > 1 ? 'style="margin-left: ' + ((level - 1) * 20) + 'px;"' : '';
            
            tocHtml += `<li ${indent}><a href="#${id}">${heading.text()}</a></li>`;
        });
        
        tocHtml += '</ul></div>';
        
        $('.report-content').prepend(tocHtml);
    }
}

// 为代码块添加复制按钮
function addCopyButtonsToCodeBlocks() {
    $('.report-content pre').each(function() {
        const pre = $(this);
        const button = $('<button class="btn btn-sm btn-outline-secondary copy-code-btn" style="position: absolute; top: 10px; right: 10px;"><i class="fas fa-copy"></i></button>');
        
        pre.css('position', 'relative');
        pre.append(button);
        
        button.on('click', function() {
            const code = pre.find('code').text();
            navigator.clipboard.writeText(code).then(function() {
                button.html('<i class="fas fa-check text-success"></i>');
                setTimeout(function() {
                    button.html('<i class="fas fa-copy"></i>');
                }, 2000);
            });
        });
    });
}

// 图片点击放大
function addImageClickHandler() {
    $('.report-content img').on('click', function() {
        const img = $(this);
        const modal = $(`
            <div class="modal fade" tabindex="-1">
                <div class="modal-dialog modal-lg modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">图片预览</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body text-center">
                            <img src="${img.attr('src')}" class="img-fluid" alt="${img.attr('alt') || ''}">
                        </div>
                    </div>
                </div>
            </div>
        `);
        
        $('body').append(modal);
        const modalInstance = new bootstrap.Modal(modal[0]);
        modalInstance.show();
        
        modal.on('hidden.bs.modal', function() {
            modal.remove();
        });
    });
    
    $('.report-content img').css('cursor', 'pointer');
}
</script>
{% endblock %}
